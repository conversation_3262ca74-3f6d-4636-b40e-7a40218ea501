'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import styled, { createGlobalStyle } from 'styled-components';
import { ArrowLeft, Loader, Save, Plus, X, Search, Filter } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import useUserStore from '@/store/userStore';
import { appTheme, appStyles } from '@/app/theme';
import { toast } from 'react-hot-toast';
import TextEditor from '@/components/TextEditor';
import { processImagesInContent, createImageUploadFunction } from '@/utils/imageUpload';

// Types
interface User {
  id: number;
  name: string;
  email: string;
  imageUrl?: string;
}

interface Organization {
  id: number;
  name: string;
}

interface Department {
  id: number;
  name: string;
  organization?: Organization;
}

interface ApiMember {
  user: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    imageUrl?: string;
  };
  departmentId: number;
}

interface TaskStatus {
  id: number;
  name: string;
  color: string;
  description?: string;
}

interface TaskAssignment {
  id: number;
  taskId: number;
  userId: number;
  assignedAt: Date;
  assignedBy?: number;
  isLeader: boolean;
  isActive: boolean;
  user: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    imageUrl?: string;
  };
}

interface Task {
  id: number;
  taskTitle: string;
  taskDescription?: string;
  createdByUser: User;
  createdByUserId: number;
  organization?: Organization;
  organizationId?: number;
  department?: Department;
  departmentId?: number;
  taskAssignments: TaskAssignment[];
  statusId: number;
  points?: number;
  dueDate?: Date | string;
  status: TaskStatus;
}

// Styled components based on CreateTaskLayout
const EditTaskContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: ${appTheme.spacing.xl};
  gap: ${appTheme.spacing.xl};
  max-width: 1400px;
  margin: 0 auto;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: ${appTheme.borderRadius.lg};

  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.lg};
    gap: ${appTheme.spacing.lg};
  }
`;

const EditTaskHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${appTheme.spacing.lg};
  border-radius: ${appTheme.borderRadius.lg};
  background-color: ${appTheme.colors.background.main};
  box-shadow: ${appTheme.shadows.sm};
`;

const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.md};
`;

const BackButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.sm};
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: ${appTheme.borderRadius.md};
  color: ${appTheme.colors.text.secondary};
  cursor: pointer;
  transition: ${appTheme.transitions.default};

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    color: ${appTheme.colors.text.primary};
  }
`;

const HeaderTitle = styled.h1`
  font-size: ${appTheme.typography.fontSizes['3xl']};
  font-weight: ${appTheme.typography.fontWeights.bold};
  color: ${appTheme.colors.text.primary};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.md};

  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: ${appTheme.typography.fontSizes['2xl']};
  }
`;

const EditTaskContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${appTheme.spacing.xl};
`;

const ContentArea = styled.div`
  border-radius: ${appTheme.borderRadius.lg};
  padding: ${appTheme.spacing.xl};
  background-color: ${appTheme.colors.background.main};
  box-shadow: ${appTheme.shadows.sm};
  overflow: hidden;
`;

const FormContainer = styled.form`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: ${appTheme.spacing.lg};

  @media (max-width: ${appTheme.breakpoints.md}) {
    grid-template-columns: 1fr;
    gap: ${appTheme.spacing.md};
  }
`;

const FormGroup = styled.div<{ $fullWidth?: boolean; $spanTwo?: boolean }>`
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  margin-bottom: 0.5rem;

  ${props =>
    props.$fullWidth &&
    `
    grid-column: 1 / -1;
  `}

  ${props =>
    props.$spanTwo &&
    `
    grid-column: span 2;
    
    @media (max-width: ${appTheme.breakpoints.lg}) {
      grid-column: 1 / -1;
    }
  `}
`;

const FormLabel = styled.label`
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
  display: block;
`;

const FormInput = styled.input`
  padding: ${appTheme.spacing.md};
  border: 1px solid #d1d5db;
  border-radius: ${appTheme.borderRadius.md};
  background-color: ${appTheme.colors.background.main};
  color: ${appTheme.colors.text.primary};
  font-size: ${appTheme.typography.fontSizes.sm};
  transition: ${appTheme.transitions.default};

  &:focus {
    outline: none;
    border-color: #6366f1;
    background-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }

  &:hover:not(:disabled) {
    border-color: #9ca3af;
  }

  &::placeholder {
    color: ${appTheme.colors.text.tertiary};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    border-color: #e5e7eb;
  }
`;

const FormSelect = styled.select`
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #111827;
  background-color: white;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  appearance: none;
  cursor: pointer;
  transition: all 0.15s ease-in-out;

  &:hover:not(:disabled) {
    border-color: #9ca3af;
    background-color: #f9fafb;
  }

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background-color: white;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    border-color: #e5e7eb;
    background-color: #f9fafb;
    color: #9ca3af;
  }

  option {
    padding: 0.5rem;
    color: #111827;
    background-color: white;
  }

  option:checked {
    background-color: #3b82f6;
    color: white;
  }
`;

const DisplayField = styled.div`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.875rem;
  background-color: #f9fafb;
  color: #374151;
  min-height: 2.75rem;
  display: flex;
  align-items: center;
`;

const FormActions = styled.div`
  display: flex;
  gap: ${appTheme.spacing.md};
  justify-content: flex-end;
  padding-top: ${appTheme.spacing.lg};
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  grid-column: 1 / -1;
`;

const Button = styled.button<{ $variant?: 'primary' | 'secondary' }>`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  padding: ${appTheme.spacing.md} ${appTheme.spacing.lg};
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: ${appTheme.transitions.default};
  border: none;

  ${props =>
    props.$variant === 'primary'
      ? `
    background: linear-gradient(135deg, rgb(255, 255, 255), rgb(244, 244, 244));
    color: #000;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    }
  `
      : `
    background: rgba(255, 255, 255, 0.1);
    color: ${appTheme.colors.text.secondary};
    border: 1px solid rgba(255, 255, 255, 0.2);

    &:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.2);
      color: ${appTheme.colors.text.primary};
    }
  `}

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const ErrorMessage = styled.div`
  color: #ff6b6b;
  font-size: ${appTheme.typography.fontSizes.sm};
  padding: ${appTheme.spacing.sm};
  background-color: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: ${appTheme.borderRadius.md};
`;

const LoadingSpinner = styled(Loader)`
  animation: spin 1s linear infinite;

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`;

const UserItem = styled.div`
  display: flex;
  align-items: center;
  padding: ${appTheme.spacing.sm};
  border-radius: ${appTheme.borderRadius.md};
  margin-bottom: ${appTheme.spacing.xs};
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.9));
  border: 1px solid rgba(226, 232, 240, 0.6);
  backdrop-filter: blur(10px);
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
  gap: ${appTheme.spacing.sm};

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease-in-out;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-color: rgba(99, 102, 241, 0.3);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));

    &::before {
      left: 100%;
    }
  }

  &:last-child {
    margin-bottom: 0;
  }
`;

const UserAvatar = styled.div<{ $imageUrl?: string; $size?: 'small' | 'medium' }>`
  width: ${props => (props.$size === 'small' ? '32px' : '36px')};
  height: ${props => (props.$size === 'small' ? '32px' : '36px')};
  border-radius: 50%;
  background: ${props =>
    props.$imageUrl ? `url(${props.$imageUrl})` : 'linear-gradient(135deg, #6366f1, #8b5cf6)'};
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: ${appTheme.typography.fontWeights.semibold};
  font-size: ${props =>
    props.$size === 'small' ? appTheme.typography.fontSizes.xs : appTheme.typography.fontSizes.sm};
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
  flex-shrink: 0;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
`;

const UserName = styled.span`
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: #1f2937;
  letter-spacing: -0.025em;
`;

const UserEmail = styled.span`
  font-size: ${appTheme.typography.fontSizes.xs};
  color: #6b7280;
  font-weight: 400;
`;

const UserActions = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.md};
`;

const LeaderCheckboxLabel = styled.label`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  cursor: pointer;
  user-select: none;
  padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};
  border-radius: ${appTheme.borderRadius.md};
  transition: all 0.2s ease-in-out;
  background: rgba(99, 102, 241, 0.05);
  border: 1px solid rgba(99, 102, 241, 0.1);

  &:hover {
    background: rgba(99, 102, 241, 0.1);
    border-color: rgba(99, 102, 241, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.1);
  }
`;

const LeaderText = styled.span`
  font-size: ${appTheme.typography.fontSizes.xs};
  font-weight: ${appTheme.typography.fontWeights.medium};
  color: #4f46e5;
  letter-spacing: 0.025em;
`;

const UserListContainer = styled.div`
  border: 1px solid rgba(209, 213, 219, 0.6);
  border-radius: ${appTheme.borderRadius.lg};
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
  backdrop-filter: blur(10px);
  min-height: 80px;
  max-height: 300px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
`;

const UserListHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${appTheme.spacing.sm};
  border-bottom: 1px solid rgba(229, 231, 235, 0.6);
  background: linear-gradient(135deg, rgba(249, 250, 251, 0.8), rgba(243, 244, 246, 0.6));
  backdrop-filter: blur(5px);
  border-radius: ${appTheme.borderRadius.lg} ${appTheme.borderRadius.lg} 0 0;
`;

const UserListTitle = styled.span`
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  color: ${appTheme.colors.text.primary};
`;

const AddUserButton = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.xs};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  user-select: none;

  &:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
`;

const UserListContent = styled.div`
  padding: ${appTheme.spacing.sm};
  overflow-y: auto;
  flex: 1;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(243, 244, 246, 0.5);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 3px;

    &:hover {
      background: rgba(156, 163, 175, 0.7);
    }
  }
`;

const RemoveUserButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  color: #ef4444;
  cursor: pointer;
  border-radius: ${appTheme.borderRadius.md};
  transition: all 0.2s ease-in-out;
  font-size: 14px;
  font-weight: 500;

  &:hover {
    background: rgba(239, 68, 68, 0.15);
    border-color: rgba(239, 68, 68, 0.3);
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);
  }

  &:active {
    transform: scale(0.95);
  }
`;

const EmptyUserList = styled.div`
  text-align: center;
  padding: ${appTheme.spacing.md};
  color: #9ca3af;
  font-size: ${appTheme.typography.fontSizes.xs};
  font-style: italic;
  background: linear-gradient(135deg, rgba(249, 250, 251, 0.5), rgba(243, 244, 246, 0.3));
  border-radius: ${appTheme.borderRadius.md};
  margin: ${appTheme.spacing.xs};
  border: 1px dashed rgba(156, 163, 175, 0.3);
`;

// Modal styled components
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.lg};
  padding: ${appTheme.spacing.xl};
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: ${appTheme.shadows.lg};
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${appTheme.spacing.lg};
`;

const ModalTitle = styled.h2`
  font-size: ${appTheme.typography.fontSizes.xl};
  font-weight: ${appTheme.typography.fontWeights.bold};
  color: ${appTheme.colors.text.primary};
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: ${appTheme.typography.fontSizes.xl};
  color: ${appTheme.colors.text.secondary};
  cursor: pointer;
  padding: ${appTheme.spacing.xs};
  border-radius: ${appTheme.borderRadius.sm};
  transition: ${appTheme.transitions.default};

  &:hover {
    background-color: #f3f4f6;
    color: ${appTheme.colors.text.primary};
  }
`;

const FilterContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${appTheme.spacing.sm};
  margin-bottom: ${appTheme.spacing.md};

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }

  & > div:last-child {
    grid-column: 1 / -1;
  }
`;

const ModalSearchInputContainer = styled.div`
  position: relative;
`;

const ModalSearchInput = styled.input`
  width: 100%;
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.sm} ${appTheme.spacing.sm} 2.25rem;
  border: 1px solid #d1d5db;
  border-radius: ${appTheme.borderRadius.sm};
  background-color: ${appTheme.colors.background.main};
  color: ${appTheme.colors.text.primary};
  font-size: ${appTheme.typography.fontSizes.sm};
  transition: ${appTheme.transitions.default};
  height: 36px;

  &:focus {
    outline: none;
    border-color: #6366f1;
    background-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
  }

  &::placeholder {
    color: #9ca3af;
  }
`;

const ModalSearchIcon = styled.div`
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  pointer-events: none;
`;

const ClearFiltersButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: ${appTheme.borderRadius.sm};
  font-size: ${appTheme.typography.fontSizes.xs};
  cursor: pointer;
  transition: ${appTheme.transitions.default};
  margin-bottom: ${appTheme.spacing.sm};
  height: 32px;
  font-weight: 500;

  &:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.5);
  }
`;

const FilterSummary = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};
  background-color: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: ${appTheme.borderRadius.sm};
  margin-bottom: ${appTheme.spacing.sm};
  font-size: ${appTheme.typography.fontSizes.xs};
  color: #1d4ed8;
  font-weight: 500;
`;

const SearchResultsContainer = styled.div`
  max-height: 280px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: ${appTheme.borderRadius.sm};
`;

const SearchResultItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${appTheme.spacing.sm};
  border-bottom: 1px solid #f3f4f6;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f8fafc;
  }
`;

// Add keyframes for loading spinner animation
const GlobalStyle = createGlobalStyle`
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

// Custom Checkbox Component
const CheckboxContainer = styled.div`
  position: relative;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  user-select: none;

  &:hover .checkbox-box {
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    transform: scale(1.05);
  }
`;

const CheckboxInput = styled.input`
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;

  &:disabled + .checkbox-box {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #f3f4f6;
    border-color: #d1d5db;
  }

  &:disabled ~ .checkbox-container {
    cursor: not-allowed;
  }
`;

const CheckboxBox = styled.div<{ $checked: boolean; $disabled: boolean }>`
  position: relative;
  width: 20px;
  height: 20px;
  border: 2px solid ${props => (props.$checked ? '#6366f1' : '#d1d5db')};
  border-radius: 6px;
  background: ${props =>
    props.$checked
      ? 'linear-gradient(135deg, #6366f1, #4f46e5)'
      : 'linear-gradient(135deg, #ffffff, #f8fafc)'};
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: ${props =>
    props.$checked ? '0 2px 8px rgba(99, 102, 241, 0.3)' : '0 1px 3px rgba(0, 0, 0, 0.1)'};

  ${props =>
    props.$disabled &&
    `
    opacity: 0.5;
    cursor: not-allowed;
    background: #f3f4f6;
    border-color: #d1d5db;
    box-shadow: none;
  `}

  &::after {
    content: '';
    position: absolute;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2.5px 2.5px 0;
    transform: rotate(45deg) scale(${props => (props.$checked ? '1' : '0')});
    transition: transform 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    top: 2px;
    left: 6px;
  }
`;

interface CustomCheckboxProps {
  checked: boolean;
  onChange: () => void;
  disabled?: boolean;
  className?: string;
}

const CustomCheckbox: React.FC<CustomCheckboxProps> = ({
  checked,
  onChange,
  disabled = false,
  className,
}) => {
  return (
    <CheckboxContainer className={className} onClick={disabled ? undefined : onChange}>
      <CheckboxInput type="checkbox" checked={checked} onChange={onChange} disabled={disabled} />
      <CheckboxBox className="checkbox-box" $checked={checked} $disabled={disabled} />
    </CheckboxContainer>
  );
};

// Helper function to get user initials
const getUserInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');
};

export default function EditTaskLayout() {
  const router = useRouter();
  const params = useParams();
  const taskId = params.id as string;
  const { getToken } = useAuth();
  const { userData } = useUserStore();

  const [task, setTask] = useState<Task | null>(null);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    taskTitle: '',
    taskDescription: '',
    organizationId: '',
    departmentId: '',
    statusId: '',
    points: '',
    dueDate: '',
  });
  const [assignedToUserIds, setAssignedToUserIds] = useState<string[]>([]);
  const [taskAssignments, setTaskAssignments] = useState<
    Array<{ userId: string; isLeader: boolean }>
  >([]);
  const [assignedUsers, setAssignedUsers] = useState<User[]>([]);
  const [members, setMembers] = useState<User[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [statuses, setStatuses] = useState<TaskStatus[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [permissionError, setPermissionError] = useState<string | null>(null);
  const [departmentFetchError, setDepartmentFetchError] = useState<string | null>(null);

  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchFilters, setSearchFilters] = useState({
    organizationId: '',
    departmentId: '',
    name: '',
  });
  const [modalOrganizations, setModalOrganizations] = useState<Organization[]>([]);
  const [modalDepartments, setModalDepartments] = useState<Department[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<Set<number>>(new Set());
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  // Create upload function for TextEditor
  const uploadImageFunction = useMemo(() => {
    return async (file: File): Promise<string> => {
      const token = await getToken();
      if (!token) {
        throw new Error('Authentication required');
      }
      return createImageUploadFunction(token)(file);
    };
  }, [getToken]);

  // Check permissions
  // useEffect(() => {
  //   if (userData) {
  //     if (!userData.role.isOwner) {
  //       setPermissionError('Only Boss users can edit tasks');
  //     } else {
  //       setPermissionError(null);
  //     }
  //   }
  // }, [userData]);

  // Fetch task data
  useEffect(() => {
    const fetchTask = async () => {
      if (!taskId || permissionError) return;

      try {
        setLoading(true);
        const token = await getToken();
        if (!token) return;

        const response = await fetch(`/api/v1/task?id=${taskId}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch task');
        }

        const data = await response.json();
        const taskData = data.task;
        setTask(taskData);

        // Initialize form data
        setFormData({
          taskTitle: taskData.taskTitle || '',
          taskDescription: taskData.taskDescription || '',
          organizationId: taskData.organizationId?.toString() || '',
          departmentId: taskData.departmentId?.toString() || '',
          statusId: taskData.statusId?.toString() || '',
          points: taskData.points?.toString() || '',
          dueDate: taskData.dueDate
            ? typeof taskData.dueDate === 'string'
              ? taskData.dueDate.split('T')[0]
              : new Date(taskData.dueDate).toISOString().split('T')[0]
            : '',
        });

        // Initialize assigned users
        const assignedUserIds =
          taskData.taskAssignments?.map((assignment: TaskAssignment) =>
            assignment.userId.toString()
          ) || [];
        setAssignedToUserIds(assignedUserIds);

        // Initialize task assignments with isLeader field
        const assignments =
          taskData.taskAssignments?.map((assignment: TaskAssignment) => ({
            userId: assignment.userId.toString(),
            isLeader: assignment.isLeader || false,
          })) || [];
        setTaskAssignments(assignments);

        // Initialize assigned users
        const assignedUsersData =
          taskData.taskAssignments?.map((assignment: TaskAssignment) => ({
            id: assignment.user.id,
            name: `${assignment.user.firstName} ${assignment.user.lastName}`,
            email: assignment.user.email,
            imageUrl: assignment.user.imageUrl,
          })) || [];
        setAssignedUsers(assignedUsersData);
      } catch (err) {
        console.error('Error fetching task:', err);
        setFormError('Failed to load task data');
      } finally {
        setLoading(false);
      }
    };

    fetchTask();
  }, [taskId, getToken, permissionError]);

  // Fetch organizations
  useEffect(() => {
    const fetchOrganizations = async () => {
      if (permissionError) return;

      try {
        const token = await getToken();
        if (!token) return;

        const response = await fetch('/api/v1/organization', {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch organizations');
        }

        const data = await response.json();
        setOrganizations(data.organizations || []);
      } catch (err) {
        console.error('Error fetching organizations:', err);
        setOrganizations([]);
      }
    };

    fetchOrganizations();
  }, [getToken, permissionError]);

  // Fetch task statuses
  const fetchTaskStatuses = useCallback(async () => {
    if (permissionError) return;

    try {
      const token = await getToken();
      if (!token) return;

      const response = await fetch('/api/v1/task-status', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch task statuses');
      }

      const data = await response.json();
      setStatuses(data.taskStatuses || []);
    } catch (err) {
      console.error('Error fetching task statuses:', err);
      setStatuses([]);
    }
  }, [getToken, permissionError]);

  useEffect(() => {
    fetchTaskStatuses();
  }, [fetchTaskStatuses]);

  // Fetch departments when organization changes
  useEffect(() => {
    const fetchDepartments = async () => {
      if (!formData.organizationId || permissionError) {
        setDepartments([]);
        setDepartmentFetchError(null);
        return;
      }

      try {
        setDepartmentFetchError(null);
        const token = await getToken();
        if (!token) return;

        const orgId = Number(formData.organizationId);
        const response = await fetch(`/api/v1/department?organizationId=${orgId}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          if (response.status === 403) {
            setDepartmentFetchError(
              `You don't have permission to access departments for this organization.`
            );
            setDepartments([]);
            return;
          }

          const errorMessage = `Failed to fetch departments: ${response.status} ${response.statusText}`;
          try {
            const errorData = await response.json();
            throw new Error(errorData.message || errorData.error || errorMessage);
          } catch {
            throw new Error(errorMessage);
          }
        }

        const data = await response.json();
        setDepartments(data.departments || []);

        // Reset department and assignee if current department is not in the list
        if (
          formData.departmentId &&
          !data.departments.some((d: Department) => d.id.toString() === formData.departmentId)
        ) {
          setFormData(prev => ({
            ...prev,
            departmentId: '',
            assignedToUserId: '',
          }));
        }
      } catch (err) {
        console.error('Error fetching departments:', err);
        setDepartmentFetchError(err instanceof Error ? err.message : 'Failed to load departments');
        setDepartments([]);
      }
    };

    fetchDepartments();
  }, [formData.organizationId, getToken, permissionError]);

  // Fetch members when department changes
  useEffect(() => {
    const fetchMembers = async () => {
      if (!formData.departmentId || permissionError || !userData?.role.isOwner) {
        setMembers([]);
        return;
      }

      try {
        const token = await getToken();
        if (!token) return;

        const deptId = Number(formData.departmentId);
        const response = await fetch(`/api/v1/member?departmentId=${deptId}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch members');
        }

        const data = await response.json();
        const apiMembers = data.members || [];

        const transformedMembers = apiMembers.map((member: ApiMember) => ({
          id: member.user.id,
          name: `${member.user.firstName} ${member.user.lastName}`,
          email: member.user.email,
          imageUrl: member.user.imageUrl,
        }));

        setMembers(transformedMembers);

        // Reset assignees if any current assignees are not in the list
        const validUserIds = transformedMembers.map((m: User) => m.id.toString());
        const filteredAssignedIds = assignedToUserIds.filter(id => validUserIds.includes(id));
        if (filteredAssignedIds.length !== assignedToUserIds.length) {
          setAssignedToUserIds(filteredAssignedIds);
        }
      } catch (err) {
        console.error('Error fetching members:', err);
        setMembers([]);
      }
    };

    fetchMembers();
  }, [formData.departmentId, getToken, permissionError, userData?.role.isOwner]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Clear form error when user starts typing
    if (formError) {
      setFormError(null);
    }
  };

  const handleDescriptionChange = (content: string) => {
    setFormData(prev => ({
      ...prev,
      taskDescription: content,
    }));

    if (formError) {
      setFormError(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!task) return;

    // Validation
    if (!formData.taskTitle.trim()) {
      setFormError('Task title is required');
      return;
    }

    // Only validate organization and department for owner/admin
    // For members, these fields are read-only and should already have values
    if (userData?.role?.isOwner || userData?.role?.isAdmin) {
      if (!formData.organizationId) {
        setFormError('Organization is required');
        return;
      }

      if (!formData.departmentId) {
        setFormError('Department is required');
        return;
      }
    }

    if (assignedToUserIds.length === 0) {
      setFormError('At least one assignee is required');
      return;
    }

    if (!formData.statusId) {
      setFormError('Status is required');
      return;
    }

    try {
      setIsSubmitting(true);
      setFormError(null);

      const token = await getToken();
      if (!token) {
        throw new Error('Authentication token not found');
      }

      // Prepare the request payload
      const payload: any = {
        id: task.id,
        taskTitle: formData.taskTitle.trim(),
        taskDescription: formData.taskDescription.trim(),
        assignedToUserIds: assignedToUserIds.map(id => parseInt(id)),
        taskAssignments: taskAssignments.map(assignment => ({
          userId: parseInt(assignment.userId),
          isLeader: assignment.isLeader,
        })),
        statusId: parseInt(formData.statusId),
        points: formData.points ? parseInt(formData.points) : null,
        dueDate: formData.dueDate || null,
      };

      // Only include organizationId and departmentId if user is owner/admin
      if (userData?.role?.isOwner || userData?.role?.isAdmin) {
        payload.organizationId = parseInt(formData.organizationId);
        payload.departmentId = parseInt(formData.departmentId);
      }

      const response = await fetch('/api/v1/task', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update task');
      }

      toast.success('Task updated successfully!');
      router.push(`/kanban/${task.id}`);
    } catch (err) {
      console.error('Error updating task:', err);
      setFormError(err instanceof Error ? err.message : 'Failed to update task');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  // Modal functions
  const openModal = async () => {
    setIsModalOpen(true);

    // Use organizations from userData.organizations instead of API (like CreateTaskLayout)
    if (modalOrganizations.length === 0 && userData?.organizations) {
      try {
        // Map userData.organizations to the format expected by modal
        const orgs = userData.organizations.map(org => ({
          id: org.id,
          name: org.name,
        }));
        setModalOrganizations(orgs);

        // If there are organizations, set the first one as default and trigger search
        if (orgs.length > 0) {
          const firstOrgId = orgs[0].id.toString();
          setSearchFilters(prev => ({ ...prev, organizationId: firstOrgId }));

          // Fetch departments for the first organization (still from API)
          const token = await getToken();
          if (token) {
            const deptResponse = await fetch(`/api/v1/department?organizationId=${firstOrgId}`, {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            });

            if (deptResponse.ok) {
              const deptData = await deptResponse.json();
              setModalDepartments(deptData.departments || []);
            }
          }

          // Trigger initial search with first organization
          searchUsers({ organizationId: firstOrgId, departmentId: '', name: '' });
        }
      } catch (err) {
        console.error('Error setting up organizations for modal:', err);
      }
    } else if (modalOrganizations.length > 0) {
      // If organizations are already loaded, trigger search with current filters or first org
      const currentOrg = searchFilters.organizationId || modalOrganizations[0]?.id.toString();
      if (currentOrg) {
        searchUsers({ ...searchFilters, organizationId: currentOrg });
      }
    }
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSearchResults([]);
    setSearchFilters({ organizationId: '', departmentId: '', name: '' });
    setModalDepartments([]);
    setSelectedUsers(new Set());
    if (searchTimeout) {
      clearTimeout(searchTimeout);
      setSearchTimeout(null);
    }
  };

  // Debounced search function
  const debouncedSearch = (filters: typeof searchFilters) => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = setTimeout(() => {
      searchUsers(filters);
    }, 500); // 500ms debounce

    setSearchTimeout(timeout);
  };

  // Handle search filter changes
  const handleSearchFilterChange = async (field: string, value: string) => {
    const newFilters = { ...searchFilters, [field]: value };
    setSearchFilters(newFilters);

    // If organization changed, fetch departments for that organization
    if (field === 'organizationId' && value) {
      try {
        const token = await getToken();
        if (!token) return;

        const response = await fetch(`/api/v1/department?organizationId=${value}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          setModalDepartments(data.departments || []);
        }
      } catch (err) {
        console.error('Error fetching departments for modal:', err);
        setModalDepartments([]);
      }
    } else if (field === 'organizationId' && !value) {
      setModalDepartments([]);
      newFilters.departmentId = '';
    }

    // Trigger debounced search if we have search criteria
    if (newFilters.organizationId || newFilters.departmentId || newFilters.name.trim()) {
      debouncedSearch(newFilters);
    } else {
      setSearchResults([]);
    }
  };

  // Search for users
  const searchUsers = async (filters = searchFilters) => {
    if (!filters.organizationId && !filters.departmentId && !filters.name.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const token = await getToken();
      if (!token) return;

      let url = '/api/v1/member?';
      const params = new URLSearchParams();

      if (filters.departmentId) {
        params.append('departmentId', filters.departmentId);
      } else if (filters.organizationId) {
        params.append('organizationId', filters.organizationId);
      }

      if (filters.name.trim()) {
        params.append('name', filters.name.trim());
      }

      const response = await fetch(url + params.toString(), {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        const apiMembers = data.members || [];

        // Transform API response to match our User interface
        const transformedMembers: User[] = apiMembers.map((member: ApiMember) => ({
          id: member.user.id,
          name: `${member.user.firstName} ${member.user.lastName}`,
          email: member.user.email,
          imageUrl: member.user.imageUrl,
        }));

        setSearchResults(transformedMembers);
      }
    } catch (err) {
      console.error('Error searching users:', err);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Toggle user selection in modal
  const toggleUserSelection = (user: User) => {
    setSelectedUsers(prev => {
      const newSet = new Set(prev);
      if (newSet.has(user.id)) {
        newSet.delete(user.id);
      } else {
        newSet.add(user.id);
      }
      return newSet;
    });
  };

  // Add selected users to assigned list
  const addSelectedUsersToList = () => {
    const usersToAdd = searchResults.filter(
      user => selectedUsers.has(user.id) && !assignedUsers.find(u => u.id === user.id)
    );

    if (usersToAdd.length > 0) {
      setAssignedUsers(prev => [...prev, ...usersToAdd]);
      setAssignedToUserIds(prev => [...prev, ...usersToAdd.map(u => u.id.toString())]);
      setTaskAssignments(prev => [
        ...prev,
        ...usersToAdd.map(u => ({ userId: u.id.toString(), isLeader: false })),
      ]);
    }

    closeModal();
  };

  // Clear all search filters
  const clearSearchFilters = () => {
    setSearchFilters({
      organizationId: '',
      departmentId: '',
      name: '',
    });
    setSearchResults([]);
    setSelectedUsers(new Set());
    setModalDepartments([]);
  };

  // Remove user from assigned list
  const removeUserFromList = (userId: number) => {
    setAssignedUsers(prev => prev.filter(u => u.id !== userId));
    setAssignedToUserIds(prev => prev.filter(id => id !== userId.toString()));
    setTaskAssignments(prev => prev.filter(assignment => assignment.userId !== userId.toString()));
  };

  // Helper function to get user initials
  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (loading) {
    return (
      <EditTaskContainer>
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '200px',
          }}
        >
          <LoadingSpinner size={32} />
        </div>
      </EditTaskContainer>
    );
  }

  if (permissionError) {
    return (
      <EditTaskContainer>
        <ErrorMessage>{permissionError}</ErrorMessage>
      </EditTaskContainer>
    );
  }

  if (!task) {
    return (
      <EditTaskContainer>
        <ErrorMessage>Task not found</ErrorMessage>
      </EditTaskContainer>
    );
  }

  return (
    <EditTaskContainer>
      <GlobalStyle />
      <EditTaskHeader>
        <HeaderLeft>
          <BackButton onClick={handleBack}>
            <ArrowLeft size={16} />
            Back
          </BackButton>
          <HeaderTitle>Edit Task</HeaderTitle>
        </HeaderLeft>
      </EditTaskHeader>

      <EditTaskContent>
        <ContentArea>
          <FormContainer onSubmit={handleSubmit}>
            <FormGroup $fullWidth>
              <FormLabel htmlFor="taskTitle">Task Title *</FormLabel>
              <FormInput
                id="taskTitle"
                name="taskTitle"
                value={formData.taskTitle}
                onChange={handleInputChange}
                placeholder="Enter task title"
                required
              />
            </FormGroup>

            <FormGroup>
              <FormLabel htmlFor="organizationId">Organization *</FormLabel>
              {userData?.role?.isOwner || userData?.role?.isAdmin ? (
                <FormSelect
                  id="organizationId"
                  name="organizationId"
                  value={formData.organizationId}
                  onChange={handleInputChange}
                  required
                >
                  <option value="">Select Organization</option>
                  {organizations.map(org => (
                    <option key={org.id} value={org.id}>
                      {org.name}
                    </option>
                  ))}
                </FormSelect>
              ) : (
                <DisplayField>
                  {task.organization?.name || 'No organization selected'}
                </DisplayField>
              )}
            </FormGroup>

            <FormGroup>
              <FormLabel htmlFor="departmentId">Department *</FormLabel>
              {userData?.role?.isOwner || userData?.role?.isAdmin ? (
                <FormSelect
                  id="departmentId"
                  name="departmentId"
                  value={formData.departmentId}
                  onChange={handleInputChange}
                  required
                  disabled={!formData.organizationId}
                >
                  <option value="">
                    {formData.organizationId ? 'Select Department' : 'Select an organization first'}
                  </option>
                  {departments.map(dept => (
                    <option key={dept.id} value={dept.id}>
                      {dept.name}
                    </option>
                  ))}
                </FormSelect>
              ) : (
                <DisplayField>
                  {departments.find(dept => dept.id.toString() === formData.departmentId)?.name || 'No department selected'}
                </DisplayField>
              )}
              {departmentFetchError && <ErrorMessage>{departmentFetchError}</ErrorMessage>}
            </FormGroup>

            {/* Assigned Users */}
            <UserListContainer>
              <UserListHeader>
                <UserListTitle>Assigned Users</UserListTitle>
                <AddUserButton onClick={openModal}>
                  <Plus size={16} />
                  Add User
                </AddUserButton>
              </UserListHeader>
              <UserListContent>
                {assignedUsers.length === 0 ? (
                  <EmptyUserList>No users assigned</EmptyUserList>
                ) : (
                  assignedUsers.map(user => (
                    <UserItem key={user.id}>
                      <UserAvatar>
                        {user.imageUrl ? (
                          <img src={user.imageUrl} alt={user.name} />
                        ) : (
                          getUserInitials(user.name)
                        )}
                      </UserAvatar>
                      <UserInfo>
                        <UserName>{user.name}</UserName>
                        <UserEmail>{user.email}</UserEmail>
                      </UserInfo>
                      <UserActions>
                        <LeaderCheckboxLabel>
                          <CustomCheckbox
                            checked={
                              taskAssignments.find(
                                assignment => assignment.userId === user.id.toString()
                              )?.isLeader || false
                            }
                            onChange={() => {
                              const userId = user.id.toString();
                              setTaskAssignments(prev =>
                                prev.map(assignment =>
                                  assignment.userId === userId
                                    ? { ...assignment, isLeader: !assignment.isLeader }
                                    : assignment
                                )
                              );
                            }}
                          />
                          <LeaderText>Leader</LeaderText>
                        </LeaderCheckboxLabel>
                        <RemoveUserButton
                          onClick={() => removeUserFromList(user.id)}
                          title="Remove user"
                        >
                          <X size={16} />
                        </RemoveUserButton>
                      </UserActions>
                    </UserItem>
                  ))
                )}
              </UserListContent>
            </UserListContainer>

            <FormGroup>
              <FormLabel htmlFor="statusId">Status *</FormLabel>
              <FormSelect
                id="statusId"
                name="statusId"
                value={formData.statusId}
                onChange={handleInputChange}
                required
              >
                <option value="">Select Status</option>
                {statuses.map(status => (
                  <option key={status.id} value={status.id}>
                    {status.name}
                  </option>
                ))}
              </FormSelect>
            </FormGroup>

            <FormGroup>
              <FormLabel htmlFor="points">Points</FormLabel>
              <FormInput
                id="points"
                name="points"
                type="number"
                min="0"
                value={formData.points}
                onChange={handleInputChange}
                placeholder="Enter story points"
              />
            </FormGroup>

            <FormGroup>
              <FormLabel htmlFor="dueDate">Due Date</FormLabel>
              <FormInput
                id="dueDate"
                name="dueDate"
                type="date"
                value={formData.dueDate}
                onChange={handleInputChange}
              />
            </FormGroup>

            <FormGroup $fullWidth>
              <FormLabel htmlFor="taskDescription">Description</FormLabel>
              <TextEditor
                value={formData.taskDescription}
                onChange={handleDescriptionChange}
                placeholder="Enter task description"
                minHeight={150}
                maxHeight={1200}
                uploadImage={uploadImageFunction}
              />
            </FormGroup>

            {formError && <ErrorMessage>{formError}</ErrorMessage>}

            <FormActions>
              <Button type="button" onClick={handleBack} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button type="submit" $variant="primary" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <LoadingSpinner size={16} />
                    Updating...
                  </>
                ) : (
                  <>
                    <Save size={16} />
                    Update Task
                  </>
                )}
              </Button>
            </FormActions>
          </FormContainer>
        </ContentArea>
      </EditTaskContent>

      {/* Modal for adding users */}
      {isModalOpen && (
        <ModalOverlay onClick={closeModal}>
          <ModalContent onClick={e => e.stopPropagation()}>
            <ModalHeader>
              <ModalTitle>Add Users to Task</ModalTitle>
              <CloseButton onClick={closeModal}>
                <X size={20} />
              </CloseButton>
            </ModalHeader>

            <FilterContainer>
              <FormGroup>
                <FormLabel>Organization</FormLabel>
                <FormSelect
                  value={searchFilters.organizationId}
                  onChange={e => handleSearchFilterChange('organizationId', e.target.value)}
                >
                  <option value="">All Organizations</option>
                  {modalOrganizations.map(org => (
                    <option key={org.id} value={org.id}>
                      {org.name}
                    </option>
                  ))}
                </FormSelect>
              </FormGroup>

              <FormGroup>
                <FormLabel>Department</FormLabel>
                <FormSelect
                  value={searchFilters.departmentId}
                  onChange={e => handleSearchFilterChange('departmentId', e.target.value)}
                  disabled={!searchFilters.organizationId}
                >
                  <option value="">
                    {searchFilters.organizationId ? 'All Departments' : 'Select organization first'}
                  </option>
                  {modalDepartments.map(dept => (
                    <option key={dept.id} value={dept.id}>
                      {dept.name}
                    </option>
                  ))}
                </FormSelect>
              </FormGroup>

              <FormGroup>
                <FormLabel>Name</FormLabel>
                <ModalSearchInputContainer>
                  <ModalSearchIcon>
                    <Search size={16} />
                  </ModalSearchIcon>
                  <ModalSearchInput
                    type="text"
                    placeholder="Search by name or email..."
                    value={searchFilters.name}
                    onChange={e => handleSearchFilterChange('name', e.target.value)}
                  />
                </ModalSearchInputContainer>
              </FormGroup>
            </FilterContainer>

            {(searchFilters.organizationId ||
              searchFilters.departmentId ||
              searchFilters.name.trim()) && (
              <>
                <ClearFiltersButton onClick={clearSearchFilters}>
                  <X size={14} />
                  Clear Filters
                </ClearFiltersButton>

                <FilterSummary>
                  <Filter size={14} />
                  <span>
                    Filtering by:{' '}
                    {searchFilters.organizationId && (
                      <span>
                        {modalOrganizations.find(
                          org => org.id.toString() === searchFilters.organizationId
                        )?.name || 'Organization'}
                        {(searchFilters.departmentId || searchFilters.name.trim()) && ', '}
                      </span>
                    )}
                    {searchFilters.departmentId && (
                      <span>
                        {modalDepartments.find(
                          dept => dept.id.toString() === searchFilters.departmentId
                        )?.name || 'Department'}
                        {searchFilters.name.trim() && ', '}
                      </span>
                    )}
                    {searchFilters.name.trim() && <span>Name: "{searchFilters.name}"</span>}
                  </span>
                </FilterSummary>
              </>
            )}

            {isSearching && (
              <div
                style={{
                  textAlign: 'center',
                  padding: appTheme.spacing.sm,
                  color: appTheme.colors.text.secondary,
                  fontSize: appTheme.typography.fontSizes.sm,
                }}
              >
                <LoadingSpinner size={14} style={{ marginRight: appTheme.spacing.xs }} />
                Searching...
              </div>
            )}

            <SearchResultsContainer>
              {searchResults.length === 0 ? (
                <div style={{ padding: '2rem', textAlign: 'center', color: '#6b7280' }}>
                  {searchFilters.organizationId ||
                  searchFilters.departmentId ||
                  searchFilters.name.trim()
                    ? 'No users found matching your criteria'
                    : 'Use the filters above to search for users'}
                </div>
              ) : (
                searchResults.map(user => (
                  <SearchResultItem key={user.id}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                      <UserAvatar>
                        {user.imageUrl ? (
                          <img src={user.imageUrl} alt={user.name} />
                        ) : (
                          getUserInitials(user.name)
                        )}
                      </UserAvatar>
                      <UserInfo>
                        <UserName>{user.name}</UserName>
                        <UserEmail>{user.email}</UserEmail>
                      </UserInfo>
                    </div>
                    <CustomCheckbox
                      checked={selectedUsers.has(user.id)}
                      onChange={() => toggleUserSelection(user)}
                      disabled={assignedUsers.some(u => u.id === user.id)}
                    />
                  </SearchResultItem>
                ))
              )}
            </SearchResultsContainer>

            <div
              style={{
                display: 'flex',
                justifyContent: 'flex-end',
                gap: '0.75rem',
                marginTop: '1.5rem',
              }}
            >
              <Button onClick={closeModal}>Cancel</Button>
              <Button
                $variant="primary"
                onClick={addSelectedUsersToList}
                disabled={selectedUsers.size === 0}
              >
                Add Selected ({selectedUsers.size})
              </Button>
            </div>
          </ModalContent>
        </ModalOverlay>
      )}
    </EditTaskContainer>
  );
}
